<?php

use Livewire\Volt\Component;

new class extends Component {
    /**
     * 获取课程特定的初始代码
     * 根据不同的课程返回不同的示例代码
     */
    public function getCourseSpecificCode()
    {
        // 这里可以根据当前课程的 slug 或其他标识符返回不同的代码
        // 例如：从数据库获取课程相关的示例代码

        // 使用 heredoc 语法来处理复杂的多行代码
        // prettier-ignore
        return <<<'PYTHON'
import streamlit as st
from google import genai
from google.genai import types
import io
import httpx
import pandas as pd

client = genai.Client(
    api_key=st.secrets["GEMINI_KEY"],
    http_options={
        "timeout": 300000
    }
)
PYTHON;
    }
}; ?>

<main class="flex min-h-full">
    <div class="mx-auto w-full flex-1">
        <div>
            <x-pdfviewer url="https://r2.ledo.world/uPic/AqRpFv.pdf" />
        </div>
        <div class="mx-auto max-w-7xl">Description</div>
    </div>

    <flux:sidebar sticky class="bg-[#292A36] p-0! [:where(&)]:w-[700px]">
        <x-runner :initial-code="$this->getCourseSpecificCode()" />
    </flux:sidebar>
</main>
