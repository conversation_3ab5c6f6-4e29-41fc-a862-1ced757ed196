@props([
    "url",
])

<div
    class="relative aspect-video max-h-[calc(100vh-56px)] w-full overflow-hidden border-b bg-zinc-50"
    x-data="pdfViewer('{{ $url }}')"
    x-init="
        init()
        $nextTick(() => fitToContainer())
    "
    x-ref="pdfContainer"
>
    {{-- 加载状态 --}}
    <div x-show="isLoading" class="absolute inset-0 flex items-center justify-center bg-zinc-100">
        <div class="flex flex-col items-center gap-3">
            <div class="h-8 w-8 animate-spin rounded-full border-4 border-zinc-300 border-t-blue-600"></div>
            <p class="text-sm text-zinc-600">正在加载 PDF...</p>
        </div>
    </div>

    {{-- 错误状态 --}}
    <div x-show="error" class="absolute inset-0 flex items-center justify-center bg-zinc-100">
        <div class="flex flex-col items-center gap-3 text-center">
            <div class="rounded-full bg-red-100 p-3">
                <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                </svg>
            </div>
            <div>
                <p class="font-medium text-zinc-900">加载失败</p>
                <p class="text-sm text-zinc-600" x-text="error"></p>
            </div>
        </div>
    </div>

    {{-- PDF 画布 --}}
    <div x-show="!isLoading && !error" class="flex h-full w-full items-center justify-center">
        <canvas x-ref="pdfCanvas" class="max-h-full max-w-full" style="display: block"></canvas>
    </div>

    {{-- 翻页控制 --}}
    <div
        x-show="! isLoading && ! error && totalPages > 1"
        class="absolute bottom-8 left-1/2 flex -translate-x-1/2 items-center gap-x-2 rounded-lg bg-zinc-800/80 p-2 text-sm text-white backdrop-blur-sm"
    >
        <flux:button
            square
            variant="ghost"
            size="sm"
            @click="prevPage()"
            :disabled="currentPage <= 1"
            class="text-white hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50"
        >
            <flux:icon.chevron-left variant="mini" class="text-white" />
        </flux:button>

        <span class="px-2 font-medium" x-text="`${currentPage} / ${totalPages}`"></span>

        <flux:button
            square
            variant="ghost"
            size="sm"
            @click="nextPage()"
            :disabled="currentPage >= totalPages"
            class="text-white hover:bg-white/20 disabled:cursor-not-allowed disabled:opacity-50"
        >
            <flux:icon.chevron-right variant="mini" class="text-white" />
        </flux:button>
    </div>
</div>

{{-- 监听窗口大小变化，重新适应容器 --}}
<script>
    window.addEventListener('resize', () => {
        // 延迟执行以确保容器尺寸已更新
        setTimeout(() => {
            const pdfViewers = document.querySelectorAll('[x-data*="pdfViewer"]');
            pdfViewers.forEach((viewer) => {
                if (viewer._x_dataStack && viewer._x_dataStack[0].fitToContainer) {
                    viewer._x_dataStack[0].fitToContainer();
                }
            });
        }, 100);
    });
</script>
