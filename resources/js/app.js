import * as pdfjsLib from "pdfjs-dist";

// 设置 PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = new URL("pdfjs-dist/build/pdf.worker.mjs", import.meta.url).toString();

// PDF 阅读器组件
document.addEventListener("alpine:init", () => {
    Alpine.data("pdfViewer", (url) => ({
        pdfDoc: null,
        currentPage: 1,
        totalPages: 0,
        scale: 1.5,
        canvas: null,
        ctx: null,
        isLoading: true,
        error: null,
        pageRendering: false,
        pageNumPending: null,

        async init() {
            this.canvas = this.$refs.pdfCanvas;
            this.ctx = this.canvas.getContext("2d");

            try {
                await this.loadPDF(url);
            } catch (error) {
                console.error("PDF 加载失败:", error);
                this.error = "无法加载 PDF 文件";
                this.isLoading = false;
            }
        },

        async loadPDF(pdfUrl) {
            try {
                this.isLoading = true;
                this.error = null;

                const loadingTask = pdfjsLib.getDocument(pdfUrl);
                this.pdfDoc = await loadingTask.promise;
                this.totalPages = this.pdfDoc.numPages;

                await this.renderPage(this.currentPage);
                this.isLoading = false;
            } catch (error) {
                console.error("PDF 加载错误:", error);
                this.error = "加载 PDF 时出错: " + error.message;
                this.isLoading = false;
            }
        },

        async renderPage(pageNum) {
            if (this.pageRendering) {
                this.pageNumPending = pageNum;
                return;
            }

            this.pageRendering = true;

            try {
                const page = await this.pdfDoc.getPage(pageNum);

                // 计算视口
                const viewport = page.getViewport({ scale: this.scale });

                // 支持高分辨率屏幕
                const outputScale = window.devicePixelRatio || 1;

                // 设置画布尺寸
                this.canvas.width = Math.floor(viewport.width * outputScale);
                this.canvas.height = Math.floor(viewport.height * outputScale);
                this.canvas.style.width = Math.floor(viewport.width) + "px";
                this.canvas.style.height = Math.floor(viewport.height) + "px";

                const transform = outputScale !== 1 ? [outputScale, 0, 0, outputScale, 0, 0] : null;

                // 渲染页面
                const renderContext = {
                    canvasContext: this.ctx,
                    transform: transform,
                    viewport: viewport,
                };

                await page.render(renderContext).promise;

                this.currentPage = pageNum;
                this.pageRendering = false;

                // 如果有待渲染的页面，继续渲染
                if (this.pageNumPending !== null) {
                    this.renderPage(this.pageNumPending);
                    this.pageNumPending = null;
                }
            } catch (error) {
                console.error("页面渲染错误:", error);
                this.pageRendering = false;
            }
        },

        prevPage() {
            if (this.currentPage <= 1) return;
            this.renderPage(this.currentPage - 1);
        },

        nextPage() {
            if (this.currentPage >= this.totalPages) return;
            this.renderPage(this.currentPage + 1);
        },

        // 自适应容器大小
        fitToContainer() {
            if (!this.pdfDoc || !this.canvas) return;

            const container = this.$refs.pdfContainer;
            const containerWidth = container.clientWidth;
            const containerHeight = container.clientHeight;

            // 获取当前页面的原始尺寸
            this.pdfDoc.getPage(this.currentPage).then((page) => {
                const viewport = page.getViewport({ scale: 1 });

                // 计算适合容器的缩放比例
                const scaleX = containerWidth / viewport.width;
                const scaleY = containerHeight / viewport.height;
                this.scale = Math.min(scaleX, scaleY) * 0.95; // 留一点边距

                this.renderPage(this.currentPage);
            });
        },
    }));
});
